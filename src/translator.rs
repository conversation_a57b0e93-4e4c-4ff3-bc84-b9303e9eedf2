use anyhow::Result;
use anyhow::anyhow;
use std::{
    fs,
    fs::File,
    io::{<PERSON><PERSON><PERSON><PERSON>, BufReader},
};

use crate::machine_code_translator::read_nopcode;
use crate::{MappingArg, RunnableCode, RunnableCodeType};

#[derive(Clone, Debug, Eq, Hash, PartialEq)]
pub enum Token {
    Space,
    Tab,
    LineFeed,
}

/// Stores mapping from ASM/bin opcode to Whitespace char
#[derive(Clone)]
pub struct Mapping<'a> {
    pub space: &'a str,
    pub tab: &'a str,
    pub line_feed: &'a str,
}

impl<'a> Mapping<'a> {
    pub fn from_code_type(rct: RunnableCodeType) -> Self {
        match rct {
            RunnableCodeType::Assembly(_) => Mapping {
                space: "unimplemented",
                tab: "unimplemented",
                line_feed: "unimplemented",
            },
            RunnableCodeType::MachineCode(_) => Mapping {
                space: "D503201F", // "Official" ARM64 NOP
                tab: "8440007F", // XZR = XZR + 0
                line_feed: "9140007F", // XZR = XZR - 0
            },
            RunnableCodeType::Whitespace(_) => Mapping {
                space: " ",
                tab: "\t",
                line_feed: "\n",
            },
        }
    }

    pub fn map(&self, string: &'a str) -> Result<Token> {
        if string == self.space {
            Ok(Token::Space)
        } else if string == self.tab {
            Ok(Token::Tab)
        } else if string == self.line_feed {
            Ok(Token::LineFeed)
        } else {
            Err(anyhow!(
                "Missing mapping: {}, not Space, Tab, or LF",
                string
            ))
        }
    }

    pub fn map_back(&self, tokens: Vec<Token>) -> String {
        let mut ret_str = String::new();
        for token in tokens {
            match token {
                Token::Space => ret_str.push_str(self.space),
                Token::Tab => ret_str.push_str(self.tab),
                Token::LineFeed => ret_str.push_str(self.line_feed),
            }
        }

        ret_str
    }

    pub fn from_args(code: Option<RunnableCode>, map: &'a MappingArg) -> Result<Self> {
        let mut mapping = Mapping {
            space: "",
            tab: "",
            line_feed: "",
        };

        if let Some(c) = code {
            mapping = Self::from_code_type(c.to_enum());
        } else {
            // requires all 3
            let needs_args_err = anyhow!("Needs more args! Can't create valid mapping");
            if map.space == None || map.tab == None || map.line_feed == None {
                return Err(needs_args_err);
            }
        }

        if let Some(_) = map.space {
            mapping.space = &map.space.as_ref().unwrap();
        }

        if let Some(_) = map.tab {
            mapping.tab = &map.tab.as_ref().unwrap();
        }

        if let Some(_) = map.line_feed {
            mapping.line_feed = &map.line_feed.as_ref().unwrap();
        }

        Ok(mapping)
    }
}

/// Translate file (depending on code type)
pub fn translate_file(file_name: &str, mp: Mapping, rct: RunnableCodeType) -> Result<Vec<Token>> {
    match rct {
        RunnableCodeType::Assembly(_) => unimplemented!(),
        RunnableCodeType::Whitespace(_) => translate_ws_file(file_name, mp),
        RunnableCodeType::MachineCode(_) => {
            let buffer = fs::read(&file_name)?;
            read_nopcode(buffer, mp)
        }
    }
}

fn translate_ws_file(file_name: &str, mp: Mapping) -> Result<Vec<Token>> {
    let mut tokens: Vec<Token> = Vec::new();

    let file = File::open(file_name)?;
    let mut reader = BufReader::new(file);

    let mut buf = Vec::<u8>::new();

    while reader
        .read_until(b'\n', &mut buf)
        .expect("read_until failed")
        != 0
    {
        // this moves the ownership of the read data to s
        // there is no allocation

        let s = String::from_utf8(buf).expect("from_utf8 failed");
        for c in s.chars() {
            match c {
                val if format!("{}", val) == mp.space => tokens.push(Token::Space),
                val if format!("{}", val) == mp.tab => tokens.push(Token::Tab),
                val if format!("{}", val) == mp.line_feed => tokens.push(Token::LineFeed),
                _ => {}
            }
        }

        // this returns the ownership of the read data to buf
        // there is no allocation
        buf = s.into_bytes();
        buf.clear();
    }

    Ok(tokens)
}
