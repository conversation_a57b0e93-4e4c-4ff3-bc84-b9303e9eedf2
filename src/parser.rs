use anyhow::{Result, anyhow};
use std::collections::HashMap;

use crate::translator::Token;

enum ExecState {
    IMP,
    Op(IMPValue),
    Parameter(InstructionAndVal),
}

enum IMPValue {
    StackManipulation,
    Arithmetic,
    HeapAccess,
    FlowControl,
    IO,
}

impl IMPValue {
    fn map(tokens: Vec<Token>) -> Result<Self> {
        if tokens == vec![Token::Space] {
            Ok(Self::StackManipulation)
        } else if tokens == vec![Token::Tab, Token::Space] {
            Ok(Self::Arithmetic)
        } else if tokens == vec![Token::Tab, Token::Tab] {
            Ok(Self::HeapAccess)
        } else if tokens == vec![Token::LineFeed] {
            Ok(Self::FlowControl)
        } else if tokens == vec![Token::Tab, Token::LineFeed] {
            Ok(Self::IO)
        } else if tokens.len() >= 3 {
            panic!("instructions misaligned")
        } else {
            Err(anyhow!("waiting for more tokens"))
        }
    }
}

/// Represents an in progress Whitespace command
struct InstructionBuilder {
    imp: Vec<Token>,
    op: Vec<Token>,
    parameter: Vec<Token>,
    exec: ExecState,
}

struct InstructionBuildResult {
    instruction: Option<InstructionAndVal>,
    new_label: Option<LabelName>,
}

impl InstructionBuilder {
    /// Constructs new command
    fn new() -> Self {
        InstructionBuilder {
            imp: Vec::new(),
            op: Vec::new(),
            parameter: Vec::new(),
            exec: ExecState::IMP,
        }
    }

    /// Clears strings being built
    fn clear(&mut self) {
        *self = Self::new();
    }

    /// Adds value to command, interpreting command if completed
    fn add(&mut self, token: Token) -> Result<InstructionBuildResult> {
        let mut instruction: Option<InstructionAndVal> = None;
        let mut new_label: Option<LabelName> = None;

        // Add to whichever field is receiving
        match &self.exec {
            ExecState::IMP => {
                self.imp.push(token);

                // check valid IMP code
                let imp = IMPValue::map(self.imp.clone());
                match imp {
                    Ok(imp_val) => self.exec = ExecState::Op(imp_val),
                    Err(_) => {}
                }
            }
            ExecState::Op(imp) => {
                self.op.push(token);

                // check valid instruction
                let wrapped_vals = Instruction::map(imp, self.op.clone());
                let op;
                let p_type;

                match wrapped_vals {
                    Ok(InstructionAndVal {
                        instr_type: op_val,
                        val: p_val,
                    }) => {
                        op = op_val;
                        p_type = p_val;
                    }
                    Err(_) => {
                        return Ok(InstructionBuildResult {
                            instruction: None,
                            new_label: None,
                        });
                    }
                }

                match p_type {
                    None => {
                        instruction = Some(InstructionAndVal {
                            instr_type: op,
                            val: None,
                        });
                        self.clear();
                    }
                    // All ParamTypes
                    Some(p) => {
                        instruction = Some(InstructionAndVal {
                            instr_type: op,
                            val: Some(p),
                        });
                        self.exec = ExecState::Parameter(instruction.clone().unwrap());
                    }
                }
            }
            ExecState::Parameter(instr_and_val) => {
                self.parameter.push(token.clone());

                // parse params
                // All params are LF delimited
                if token == Token::LineFeed {
                    match &instr_and_val.val {
                        None => {}
                        Some(Param::LabelName(_)) => {
                            // value is now new label

                            instruction = Some(
                                instr_and_val.recreate(Param::LabelName(self.parameter.clone())),
                            );

                            match instruction.clone().unwrap().instr_type {
                                Instruction::Mark(_) => {
                                    new_label = Some(self.parameter.clone());
                                }
                                _ => {}
                            }

                            self.clear();
                        }
                        Some(Param::Number(_)) | Some(Param::Position(_)) => {
                            // First character represents pos/neg
                            // No character means 0? undefined behavior

                            let multiplier;
                            match self.parameter[0] {
                                Token::Space => multiplier = 1,
                                Token::LineFeed => multiplier = 0,
                                Token::Tab => multiplier = -1,
                            }

                            let mut bitstring = String::new();
                            for token in &mut self.parameter[1..] {
                                match token {
                                    Token::Space => bitstring.push_str("0"),
                                    Token::Tab => bitstring.push_str("1"),
                                    Token::LineFeed => break,
                                }
                            }

                            let num: Number = i32::from_str_radix(&bitstring, 2)? * multiplier;

                            match &instr_and_val.val {
                                Some(Param::Number(_)) => {
                                    instruction = Some(instr_and_val.recreate(Param::Number(num)));
                                }
                                Some(Param::Position(_)) => {
                                    let pos: Position =
                                        <i32 as TryInto<Position>>::try_into(num).unwrap();
                                    instruction =
                                        Some(instr_and_val.recreate(Param::Position(pos)));
                                }
                                _ => {}
                            }
                            self.clear();
                        }
                    }
                }
            }
        }

        Ok(InstructionBuildResult {
            instruction,
            new_label,
        })
    }
}

pub fn parse(tokens: Vec<Token>) -> Result<(Vec<InstructionAndVal>, Labels)> {
    let mut instructions: Vec<InstructionAndVal> = Vec::new();
    let mut labels: Labels = HashMap::new();

    let mut builder = InstructionBuilder::new();

    for token in tokens {
        let res = builder.add(token)?;
        if let Some(instruction) = res.instruction {
            instructions.push(instruction);
        }

        if let Some(label) = res.new_label {
            // New label will point instruction that was just added
            labels
                .insert(label, instructions.len())
                .ok_or(anyhow!("duplicate label"))?;
        }
    }

    Ok((instructions, labels))
}

pub type Number = i32;
type Position = usize;
type LabelName = Vec<Token>;
type Labels = HashMap<LabelName, Position>;

#[derive(Clone, Debug)]
pub enum Param {
    Number(Number),
    Position(Position),
    LabelName(LabelName),
}

#[derive(Clone, Debug)]
pub enum Instruction {
    Push(Number),
    Dup,
    CopyTop(Position),
    SwapTop,
    Pop,
    Slide(Position),
    Add,
    Sub,
    Mul,
    Div,
    Mod,
    HeapStore,
    HeapRetrieve,
    Mark(LabelName),
    CallSubroutine(LabelName),
    JumpTo(LabelName),
    JumpIfZero(LabelName),
    JumpIfNeg(LabelName),
    EndSubroutine,
    EndProgram,
    PrintChar,
    PrintNum,
    ReadCharTo,
    ReadNumTo,
}

#[derive(Clone, Debug)]
pub struct InstructionAndVal {
    pub instr_type: Instruction,
    pub val: Option<Param>,
}

impl InstructionAndVal {
    fn recreate(&self, val: Param) -> InstructionAndVal {
        InstructionAndVal {
            instr_type: self.instr_type.clone(),
            val: Some(val),
        }
    }
}

impl Instruction {
    fn map(imp: &IMPValue, tokens: Vec<Token>) -> Result<InstructionAndVal> {
        if tokens.len() >= 3 {
            panic!("instructions misaligned")
        }

        let default_number: Number = 0;
        let default_pos: Position = 0;
        let default_labelname: LabelName = vec![];

        match imp {
            IMPValue::StackManipulation => {
                if tokens == vec![Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Push(default_number),
                        val: Some(Param::Number(default_number)),
                    })
                } else if tokens == vec![Token::LineFeed, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Dup,
                        val: None,
                    })
                } else if tokens == vec![Token::Tab, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::CopyTop(default_pos),
                        val: Some(Param::Position(default_pos)),
                    })
                } else if tokens == vec![Token::LineFeed, Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::SwapTop,
                        val: None,
                    })
                } else if tokens == vec![Token::LineFeed, Token::LineFeed] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Pop,
                        val: None,
                    })
                } else if tokens == vec![Token::Tab, Token::LineFeed] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Slide(default_pos),
                        val: Some(Param::Position(default_pos)),
                    })
                } else {
                    Err(anyhow!("waiting for more tokens"))
                }
            }
            IMPValue::Arithmetic => {
                if tokens == vec![Token::Space, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Add,
                        val: None,
                    })
                } else if tokens == vec![Token::Space, Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Sub,
                        val: None,
                    })
                } else if tokens == vec![Token::Space, Token::LineFeed] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Mul,
                        val: None,
                    })
                } else if tokens == vec![Token::Tab, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Div,
                        val: None,
                    })
                } else if tokens == vec![Token::Tab, Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Mod,
                        val: None,
                    })
                } else {
                    Err(anyhow!("waiting for more tokens"))
                }
            }
            IMPValue::HeapAccess => {
                if tokens == vec![Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::HeapStore,
                        val: None,
                    })
                } else if tokens == vec![Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::HeapRetrieve,
                        val: None,
                    })
                } else {
                    Err(anyhow!("waiting for more tokens!"))
                }
            }
            IMPValue::FlowControl => {
                if tokens == vec![Token::Space, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::Mark(default_labelname.clone()),
                        val: Some(Param::LabelName(default_labelname)),
                    })
                } else if tokens == vec![Token::Space, Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::CallSubroutine(default_labelname.clone()),
                        val: Some(Param::LabelName(default_labelname)),
                    })
                } else if tokens == vec![Token::Space, Token::LineFeed] {
                    Ok(InstructionAndVal {
                        instr_type: Self::JumpTo(default_labelname.clone()),
                        val: Some(Param::LabelName(default_labelname)),
                    })
                } else if tokens == vec![Token::Tab, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::JumpIfZero(default_labelname.clone()),
                        val: Some(Param::LabelName(default_labelname)),
                    })
                } else if tokens == vec![Token::Tab, Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::JumpIfNeg(default_labelname.clone()),
                        val: Some(Param::LabelName(default_labelname)),
                    })
                } else if tokens == vec![Token::Tab, Token::LineFeed] {
                    Ok(InstructionAndVal {
                        instr_type: Self::EndSubroutine,
                        val: None,
                    })
                } else if tokens == vec![Token::LineFeed, Token::LineFeed] {
                    Ok(InstructionAndVal {
                        instr_type: Self::EndProgram,
                        val: None,
                    })
                } else {
                    Err(anyhow!("waiting for more tokens"))
                }
            }
            IMPValue::IO => {
                if tokens == vec![Token::Space, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::PrintChar,
                        val: None,
                    })
                } else if tokens == vec![Token::Space, Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::PrintNum,
                        val: None,
                    })
                } else if tokens == vec![Token::Tab, Token::Space] {
                    Ok(InstructionAndVal {
                        instr_type: Self::ReadCharTo,
                        val: None,
                    })
                } else if tokens == vec![Token::Tab, Token::Tab] {
                    Ok(InstructionAndVal {
                        instr_type: Self::ReadNumTo,
                        val: None,
                    })
                } else {
                    Err(anyhow!("waiting for more tokens"))
                }
            }
        }
    }
}
