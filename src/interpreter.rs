use anyhow::Result;
use anyhow::anyhow;
use console::Term;
use std::{char, collections::HashMap, io};

use crate::parser::Instruction;
use crate::parser::InstructionAndVal;
use crate::parser::Number;
use crate::parser::Param;
use crate::translator::Token;

pub fn execute(instructions: Vec<InstructionAndVal>) -> Result<()> {
    let mut vm = VM::new();

    if instructions.len() == 0 {
        return Err(anyhow!("No instructions found in file"));
    }

    loop {
        let program_continue = vm.run_inst(instructions[vm.program_counter].clone())?;
        if !program_continue {
            break;
        }
    }

    Ok(())
}

struct VM {
    stack: Vec<i32>,
    heap: HashMap<i32, i32>,
    call_stack: Vec<usize>,
    program_counter: usize,
    instructions: Vec<Instruction>,
    labels: HashMap<Vec<Token>, usize>,
    terminal: Term,
}

impl VM {
    fn new() -> Self {
        VM {
            stack: Vec::new(),
            heap: HashMap::new(),
            call_stack: Vec::new(),
            program_counter: 0,
            instructions: Vec::new(),
            labels: HashMap::new(),
            terminal: Term::stdout(),
        }
    }

    fn run_inst(&mut self, inst: InstructionAndVal) -> Result<bool> {
        let val = inst.val;

        let mut num = 0;
        let mut pos = 0;
        let mut label = vec![];

        if let Some(ok_val) = val {
            match ok_val {
                Param::Number(n) => num = n,
                Param::Position(p) => pos = p,
                Param::LabelName(l) => label = l,
            }
        }

        match inst.instr_type {
            Instruction::Push(_) => self.stack.push(num),
            Instruction::Dup => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                self.stack.push(a);
                self.stack.push(a);
            }
            Instruction::CopyTop(_) => {
                let a = self.stack[self.stack.len() - pos];
                self.stack.push(a);
            }
            Instruction::SwapTop => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let b = self.stack.pop().ok_or(anyhow!("stack too small"))?;

                self.stack.push(a);
                self.stack.push(b);
            }
            Instruction::Pop => {
                self.stack.pop().ok_or(anyhow!("stack too small"))?;
            }
            Instruction::Slide(_) => {
                let top = self.stack.pop().ok_or(anyhow!("stack too small"))?;

                for _ in 1..pos {
                    self.stack.pop();
                }

                self.stack.push(top);
            }
            Instruction::Add => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let b = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                self.stack.push(a + b);
            }
            Instruction::Sub => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let b = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                self.stack.push(a - b);
            }
            Instruction::Mul => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let b = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                self.stack.push(a * b);
            }
            Instruction::Div => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let b = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                self.stack.push(a / b);
            }
            Instruction::Mod => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let b = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                self.stack.push(a % b);
            }
            Instruction::HeapStore => {
                // TODO check if Store (and load) discards values?
                let address = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let val = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                self.heap.insert(address, val);
            }
            Instruction::HeapRetrieve => {
                let address = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let val = self
                    .heap
                    .get(&address)
                    .ok_or(anyhow!("val not set in heap"))?;
                self.stack.push(*val);
            }
            Instruction::Mark(_) => {
                // labels should be declared by parser, to allow for jumping to label before
                // it (would) be defined by interpreter
                self.program_counter += 1;
                return Ok(true);
            }
            Instruction::CallSubroutine(_) => {
                self.call_stack.push(self.program_counter);
                self.program_counter =
                    *self.labels.get(&label).ok_or(anyhow!("label not found!"))?;
            }
            Instruction::JumpTo(_) => {
                self.program_counter =
                    *self.labels.get(&label).ok_or(anyhow!("label not found!"))?;
            }
            Instruction::JumpIfZero(_) => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                if a == 0 {
                    self.program_counter =
                        *self.labels.get(&label).ok_or(anyhow!("label not found!"))?
                }
                self.stack.push(0);
            }
            Instruction::JumpIfNeg(_) => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                if a < 0 {
                    self.program_counter =
                        *self.labels.get(&label).ok_or(anyhow!("label not found!"))?
                }
                self.stack.push(0);
            }
            Instruction::EndSubroutine => {
                if self.call_stack.len() == 0 {
                    // Assume this means end program?
                    return Ok(false);
                }

                if let Some(a) = self.call_stack.pop() {
                    self.program_counter = a;
                }
            }
            Instruction::EndProgram => {
                return Ok(false);
            }
            Instruction::PrintChar => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let digit = char::from_u32(a.try_into().unwrap()).unwrap();
                print!("{}", digit);
                self.stack.push(a);
            }
            Instruction::PrintNum => {
                let a = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                print!("{}", a);
                self.stack.push(a);
            }
            Instruction::ReadCharTo => {
                // read single unicode char from stdin (all should fit in i32?)
                // TODO check if Term and io::stdin don't mesh well
                let ch = self.terminal.read_char()?;
                let ch_num: Number = (ch as u32).try_into().unwrap();

                let top = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let addr = self.stack.len() - top as usize;

                self.stack.splice(addr..addr, [ch_num]);
            }
            Instruction::ReadNumTo => {
                // unclear on if read number means multi or single digit?
                // assuming multi

                let mut input = String::new();
                io::stdin().read_line(&mut input)?;
                let num: Number = input.trim().parse::<i32>().expect("Failed to parse number");

                let top = self.stack.pop().ok_or(anyhow!("stack too small"))?;
                let addr = self.stack.len() - top as usize;

                self.stack.splice(addr..addr, [num]);
            }
        }

        self.program_counter += 1;

        Ok(true)
    }
}
