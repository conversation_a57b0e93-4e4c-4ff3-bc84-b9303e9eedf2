use anyhow::{Result, anyhow};
use capstone::{<PERSON>stone, prelude::*};
use goblin::error::<PERSON><PERSON><PERSON> as GoblinError;
use goblin::mach::MachO;
use hex::FromHex;

use crate::translator::{Mapping, Token};
use crate::macho_parser;

/// Working with ARM64 ISA, instructions are 4 bytes
const INSTR_LEN: usize = 4;

/// Little-Endian byte order ARM64 instructions
struct MappingBytes {
    space: [u8; 4],
    tab: [u8; 4],
    line_feed: [u8; 4],
}

impl MappingBytes {
    fn from_map(map: Mapping) -> Result<Self> {
        Ok(MappingBytes {
            space: to_bytes(string_to_instruction(map.space)?),
            tab: to_bytes(string_to_instruction(map.tab)?),
            line_feed: to_bytes(string_to_instruction(map.line_feed)?),
        })
    }
}

// Two public functions

/// Take in values of form:
/// - Machine code byte array (parsed from file by main(?))
/// - Translations to be made (vec of tuples)?
/// - Stream (vec) of Tokens
/// ~~Return translated bytecode~~ Modify in-place
pub fn insert_nopcode(
    buffer: &mut Vec<u8>,
    mapping: Mapping,
    mut tokens: Vec<Token>,
) -> Result<()> {
    // TODO include 4th opcode for nops in original file we can't overwrite?
    let mb = MappingBytes::from_map(mapping)?;

    let offsets = get_offsets(&buffer, &mb)?;

    // TODO support # nops (in source) > # tokens
    for offset in offsets {
        let token_option = tokens.pop();
        let token;

        match token_option {
            Some(Token::Space) => {
                token = mb.space;
            }
            Some(Token::Tab) => {
                token = mb.tab;
            }
            Some(Token::LineFeed) => {
                token = mb.line_feed;
            }
            None => {
                return Ok(());
            }
        }

        buffer[offset..offset + INSTR_LEN].copy_from_slice(&token);
    }

    // Test without TODO

    /* Keep track of # tokens to be added at end of __TEXT__.
     * Then go through (parse) Mach-O section headers,
     * changing size and offset when appropriate.
     * Then go in add actually add tokens.
     */

    let insertions = tokens.len(); // each one being 4 bytes

    const PAGE_ALIGN: usize = 0x1000;
    let mut num_align_bytes = (((insertions / (PAGE_ALIGN/4)) + 1) * (PAGE_ALIGN/4)) - insertions;

    println!("insertions: {insertions}, {insertions:x}");
    println!("    +");
    println!("num_align_bytes: {num_align_bytes}, {num_align_bytes:x}");
    println!("= {}, {:x}", insertions+num_align_bytes, insertions+num_align_bytes);

    // TODO parse symbol table to know where to actually insert
    // Look for symbol 0x00000016 for _start

    // TODO break up this line :sob:
    let mut insert_offset: usize = macho_parser::modify_offsets(buffer, insertions + num_align_bytes).unwrap().try_into().unwrap();

    num_align_bytes *= 4; // each being one byte

    while tokens.len() != 0 {
        let token_option = tokens.pop();

        let token;
        match token_option {
            Some(Token::Space) => {
                token = mb.space;
            }
            Some(Token::Tab) => {
                token = mb.tab;
            }
            Some(Token::LineFeed) => {
                token = mb.line_feed;
            }
            None => {
                return Ok(());
            }
        }

        //buffer.splice(buffer.len()..buffer.len(), token.iter().cloned());
        buffer.splice(insert_offset..insert_offset, token.iter().cloned());
        insert_offset += 4;
    }

    const NULL_BYTE: u8 = 0x0;
    while num_align_bytes != 0 {
        buffer.splice(insert_offset..insert_offset, std::iter::once(NULL_BYTE));
        insert_offset += 1;
        num_align_bytes -= 1;
    }

    Ok(())
}

/// Take in values of form:
/// - Machine code byte array
/// - Translation mappings
/// Returns stream (vec) of Tokens
pub fn read_nopcode(buffer: Vec<u8>, mapping: Mapping) -> Result<Vec<Token>> {
    let mb = MappingBytes::from_map(mapping)?;
    let mut tokens: Vec<Token> = Vec::new();

    let mut offsets = get_offsets(&buffer, &mb)?;
    let _ = offsets.pop();

    for offset in offsets {
        let instr: [u8; INSTR_LEN] = buffer[offset..offset + INSTR_LEN]
            .try_into()
            .expect("Failed to extract instruction from offset");

        match instr {
            val if val == mb.space => tokens.push(Token::Space),
            val if val == mb.tab => tokens.push(Token::Tab),
            val if val == mb.line_feed => tokens.push(Token::LineFeed),
            _ => {
                // Something has gone wrong with logic
                return Err(anyhow!("Instruction found can not be read!"));
            }
        }
    }

    Ok(tokens)
}

/// Gets all offsets given a mapping (ie what instructions to look for)
/// Always appends final instruction offset, to determine where to append extra
///  instructions, if need be.
fn get_offsets(buffer: &Vec<u8>, mapping: &MappingBytes) -> Result<Vec<usize>> {
    // less redundant code, but
    // TODO refactor so both functions aren't looping through possible Token enum values?

    let mach_o = match MachO::parse(&buffer, 0) {
        Ok(mach_o) => mach_o,
        Err(GoblinError::BadMagic(_)) => {
            return Err(anyhow!("Error: Not a valid Mach-O file (bad magic number)"));
        }
        Err(e) => return Err(e.into()),
    };

    let cs = Capstone::new()
        .arm64()
        .mode(arch::arm64::ArchMode::Arm)
        .build()?;

    let mut modification_offsets: Vec<usize> = Vec::new();

    for segment in &mach_o.segments {
        if segment.name().map_or(false, |name| name == "__TEXT") {
            println!(
                "Found __TEXT segment. Virtual address: 0x{:x}, Size: 0x{:x}",
                segment.vmaddr, segment.vmsize
            );

            let sections = segment.sections()?;

            for s in sections {
                let section = s.0;
                if section.name().map_or(false, |name| name == "__text") {
                    println!(
                        "Found __text section. File Offset: 0x{:x}, Size: 0x{:x}",
                        section.offset, section.size
                    );

                    let code_start_file_offset = section.offset as usize;
                    let code_end_file_offset = (section.offset + section.size as u32) as usize;

                    if code_end_file_offset > buffer.len() {
                        eprintln!("Error: Code section extends beyond file bounds.");
                        continue;
                    }

                    let code_bytes_slice = &buffer[code_start_file_offset..code_end_file_offset];
                    let code_virtual_address = section.addr;

                    let instructions = cs.disasm_all(code_bytes_slice, code_virtual_address)?;

                    for i in instructions.iter() {
                        println!(
                            "0x{:x}:\t{}\t{}",
                            i.address(),
                            i.mnemonic().unwrap(),
                            i.op_str().unwrap()
                        );

                        // little endian, bytes placed backwards
                        if i.bytes() == mapping.space
                            || i.bytes() == mapping.tab
                            || i.bytes() == mapping.line_feed
                        {
                            println!("  -> Found mapped instruction at 0x{:x}", i.address());

                            let relative_addr_in_section = i.address() - code_virtual_address;
                            let instruction_file_offset =
                                code_start_file_offset + relative_addr_in_section as usize;

                            if instruction_file_offset + i.bytes().len() > buffer.len() {
                                eprintln!(
                                    "Warning: Instruction at 0x{:x} extends beyond file buffer bounds. Skipping modification.",
                                    i.address()
                                );
                                continue;
                            }

                            modification_offsets.push(instruction_file_offset);
                        }
                    }
                }
            }
        }
    }

    Ok(modification_offsets)
}

// TODO fix docs
/// ie turns 0xd503201f -> [0x1f, 0x20, 0x03, 0xd5]
/// assuming instructions are constant 4 bytes
pub fn to_bytes(bytestring: u32) -> [u8; 4] {
    let bytes = [
        ((bytestring & 0xFF000000) >> 24) as u8,
        ((bytestring & 0x00FF0000) >> 16) as u8,
        ((bytestring & 0x0000FF00) >> 8) as u8,
        ((bytestring & 0x000000FF)) as u8,
    ];

    bytes
}

/// ie turns 0xd503201f <- [0x1f, 0x20, 0x03, 0xd5]
/// assuming instructions are constant 4 bytes
/// TODO fix docs
pub fn reverse_endian_fb(bytes: [u8; 4]) -> u32 {
    let bytestring = (bytes[3] as u32) << 24
                   | (bytes[2] as u32) << 16
                   | (bytes[1] as u32) << 8
                   | (bytes[0] as u32);

    bytestring
}

/// ie parses "d503201f" to the value 0xd503201f
fn string_to_instruction(s: &str) -> Result<u32> {
    let bytes = <[u8; 4]>::from_hex(s)?;
    Ok(u32::from_le_bytes(bytes))
}
