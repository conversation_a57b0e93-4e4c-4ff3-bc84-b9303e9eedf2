examples/hello.nopc:
Mach header
      magic  cputype cpusubtype  caps    filetype ncmds sizeofcmds      flags
MH_MAGIC_64    ARM64        ALL  0x00     EXECUTE    16        744   NOUNDEFS DYLDLINK TWOLEVEL PIE
Load command 0
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __PAGEZERO
   vmaddr 0x0000000000000000
   vmsize 0x0000000100000000
  fileoff 0
 filesize 0
  maxprot ---
 initprot ---
   nsects 0
    flags (none)
Load command 1
      cmd LC_SEGMENT_64
  cmdsize 232
  segname __TEXT
   vmaddr 0x0000000100000000
   vmsize 0x0000000000005000
  fileoff 0
 filesize 20480
  maxprot r-x
 initprot r-x
   nsects 2
    flags (none)
Section
  sectname __text
   segname __TEXT
      addr 0x0000000100003f78
      size 0x000000000000102e
    offset 16248
     align 2^2 (4)
    reloff 0
    nreloc 0
      type S_REGULAR
attributes PURE_INSTRUCTIONS SOME_INSTRUCTIONS
 reserved1 0
 reserved2 0
Section
  sectname __unwind_info
   segname __TEXT
      addr 0x0000000100004fa8
      size 0x0000000000000058
    offset 20392
     align 2^2 (4)
    reloff 0
    nreloc 0
      type S_REGULAR
attributes (none)
 reserved1 0
 reserved2 0
Load command 2
      cmd LC_SEGMENT_64
  cmdsize 72
  segname __LINKEDIT
   vmaddr 0x0000000100005000
   vmsize 0x0000000000004000
  fileoff 20480
 filesize 488
  maxprot r--
 initprot r--
   nsects 0
    flags (none)
Load command 3
      cmd LC_DYLD_CHAINED_FIXUPS
  cmdsize 16
  dataoff 20480
 datasize 56
Load command 4
      cmd LC_DYLD_EXPORTS_TRIE
  cmdsize 16
  dataoff 20536
 datasize 48
Load command 5
     cmd LC_SYMTAB
 cmdsize 24
  symoff 20592
   nsyms 3
  stroff 20640
 strsize 40
Load command 6
            cmd LC_DYSYMTAB
        cmdsize 80
      ilocalsym 0
      nlocalsym 1
     iextdefsym 1
     nextdefsym 2
      iundefsym 3
      nundefsym 0
         tocoff 0
           ntoc 0
      modtaboff 0
        nmodtab 0
   extrefsymoff 0
    nextrefsyms 0
 indirectsymoff 0
  nindirectsyms 0
      extreloff 0
        nextrel 0
      locreloff 0
        nlocrel 0
Load command 7
          cmd LC_LOAD_DYLINKER
      cmdsize 32
         name /usr/lib/dyld (offset 12)
Load command 8
     cmd LC_UUID
 cmdsize 24
    uuid BD0DC327-A8DD-3004-879F-34D89FB61DA9
Load command 9
      cmd LC_BUILD_VERSION
  cmdsize 32
 platform MACOS
    minos 14.0
      sdk 12.3
   ntools 1
     tool LD
  version 1053.12
Load command 10
      cmd LC_SOURCE_VERSION
  cmdsize 16
  version 0.0
Load command 11
       cmd LC_MAIN
   cmdsize 24
  entryoff 16248
 stacksize 0
Load command 12
          cmd LC_LOAD_DYLIB
      cmdsize 56
         name /usr/lib/libSystem.B.dylib (offset 24)
   time stamp 2 Wed Dec 31 16:00:02 1969
      current version 1345.100.2
compatibility version 1.0.0
Load command 13
      cmd LC_FUNCTION_STARTS
  cmdsize 16
  dataoff 20584
 datasize 8
Load command 14
      cmd LC_DATA_IN_CODE
  cmdsize 16
  dataoff 20592
 datasize 0
Load command 15
      cmd LC_CODE_SIGNATURE
  cmdsize 16
  dataoff 20688
 datasize 280
