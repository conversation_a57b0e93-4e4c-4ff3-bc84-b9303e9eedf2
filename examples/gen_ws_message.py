def ws_push(n):
    """Return Whitespace code to push an integer onto the stack."""
    sign = ' ' if n >= 0 else '\t'
    n_bin = bin(abs(n))[2:]
    return '  ' + sign + n_bin.replace('0', ' ').replace('1', '\t') + '\n'

def ws_output_char():
    """Return Whitespace code to output top of stack as character."""
    return '\t\n  '

def ws_exit():
    """Return Whitespace code to exit the program."""
    return '\n\n\n'

# The message to encode
message = input()

# Build the complete Whitespace program
ws_program = ''
for char in message:
    ws_program += ws_push(ord(char))
    ws_program += ws_output_char()
ws_program += ws_exit()

# Write to a .ws file
file_path = "output.ws"
with open(file_path, "w", encoding="utf-8") as f:
    f.write(ws_program)

file_path

