fn string_to_instruction(s: &str) -> Result<u32, std::num::ParseIntError> {
    // Parse hex string as big-endian u32 (as it appears in disassembly)
    Ok(u32::from_str_radix(s, 16)?)
}

fn to_bytes(bytestring: u32) -> [u8; 4] {
    // ARM64 uses little-endian byte order
    bytestring.to_le_bytes()
}

fn main() {
    // Test the NOP instruction
    let nop_str = "D503201F";
    println!("Original string: {}", nop_str);
    
    let nop_u32 = string_to_instruction(nop_str).unwrap();
    println!("As u32: 0x{:08x}", nop_u32);
    
    let nop_bytes = to_bytes(nop_u32);
    println!("As bytes: {:02x} {:02x} {:02x} {:02x}", nop_bytes[0], nop_bytes[1], nop_bytes[2], nop_bytes[3]);
    
    // What it should be for ARM64 NOP (0xd503201f in little-endian)
    let correct_nop = [0x1f, 0x20, 0x03, 0xd5];
    println!("Correct ARM64 NOP: {:02x} {:02x} {:02x} {:02x}", correct_nop[0], correct_nop[1], correct_nop[2], correct_nop[3]);
    
    println!("Match: {}", nop_bytes == correct_nop);
}
